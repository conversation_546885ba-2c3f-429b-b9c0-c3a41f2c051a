import { endSession } from './session-utils';
import { websocketLogger } from '../utils/logger';
import { globalHeartbeatManager } from './heartbeat-manager';
import type {
    WebSocketConnection,
    FlowDependencies,
    ConnectionData,
    TwilioStartMessage,
    TwilioMediaMessage
} from '../types/websocket';
import type { WebSocket } from 'ws';
import {
    extractTwilioStreamInfo,
    getValidSessionConfig,
    createTwilioConnectionData,
    logTwilioStreamInfo,
    logSessionConfiguration,
    startLifecycleManagement,
    startTwilioHeartbeat,
    sendSessionStartedMessage,
    handleGeminiSessionFailure,
    handleSessionStartError
} from './twilio-session-helpers';

// Twilio flow handler for both inbound and outbound calls
export function handleTwilioFlow(connection: WebSocketConnection, deps: FlowDependencies): void {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall
    } = deps;

    const callSid = connection.query?.CallSid || `twilio-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    websocketLogger.info(`Twilio ${flowType} call started`, { callSid, flowType, isIncomingCall });

    const ws = (connection.socket || connection) as WebSocket;

    // CRITICAL FIX: Don.js't use persistent session variables - always get fresh state from activeConnections
    // This prevents stale state issues between multiple calls
    console.log(`🔄 [${callSid}] Starting fresh Twilio flow handler`);

    // Store event listeners for cleanup
    const eventListeners = new Map<string, (...args: any[]) => void>();

    const messageHandler = async (message: Buffer | string) => {
        try {
            const data = JSON.parse(message.toString());

            // CRITICAL FIX: Better validation of message structure
            if (!data || typeof data !== 'object') {
                console.log(`⚠️ [${callSid}] Invalid message structure - not an object:`, message);
                return;
            }

            // Twilio uses 'event' field for message type
            const messageType = data.event;

            if (!messageType) {
                console.log(`⚠️ [${callSid}] Message missing 'event' field:`, JSON.stringify(data, null, 2));
                return;
            }

            websocketLogger.debug(`Twilio ${flowType} message received`, { callSid, event: data.event, messageType });

            switch (messageType) {
                case 'connected':
                    // CRITICAL FIX: Handle 'connected' event that Twilio sends for some calls
                    console.log(`🔗 [${callSid}] Twilio CONNECTED event received:`, JSON.stringify(data, null, 2));
                    websocketLogger.info('Twilio connected event received', { callSid });

                    // Update connection state to indicate Twilio WebSocket is ready
                    const connectionData = activeConnections.get(callSid);
                    if (connectionData) {
                        connectionData.twilioConnected = true;
                        connectionData.lastActivity = Date.now();
                        console.log(`✅ [${callSid}] Twilio WebSocket connection confirmed`);

                        // If we have both Twilio connection and Gemini session, mark as fully ready
                        if (connectionData.geminiSession && connectionData.isSessionActive) {
                            connectionData.fullyReady = true;
                            console.log(`🎯 [${callSid}] Session fully ready - both Twilio and Gemini connected`);
                        }
                    } else {
                        console.warn(`⚠️ [${callSid}] Received connected event but no connection data found`);
                    }
                    break;

                case 'start':
                    // Twilio sends 'start' event when stream begins
                    console.log(`🔍 [${callSid}] Twilio START event received:`, JSON.stringify(data, null, 2));
                    try {
                        await handleTwilioStartSession(callSid, data as TwilioStartMessage, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                    } catch (error) {
                        websocketLogger.error(`Failed to handle Twilio start session for ${callSid}`, error instanceof Error ? error : new Error(String(error)));
                        // Send error message to client
                        ws.send(JSON.stringify({
                            type: 'session-error',
                            error: 'Failed to initialize session. Please try again.',
                            critical: true
                        }));
                        // Clean up connection
                        activeConnections.delete(callSid);
                    }
                    break;

                case 'media': {
                    // Always get fresh session state from activeConnections
                    const connectionData = activeConnections.get(callSid);
                    const geminiSession = connectionData?.geminiSession;
                    const isSessionActive = connectionData?.isSessionActive;

                    await handleTwilioMedia(callSid, data as TwilioMediaMessage, geminiSession, isSessionActive || false, deps, activeConnections, lifecycleManager, recoveryManager);
                    break;
                }

                case 'mark':
                    websocketLogger.debug('Mark received', { callSid });
                    break;

                case 'stop':
                    websocketLogger.info('Stop message received from Twilio', { callSid });
                    await handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager);
                    break;

                default:
                    websocketLogger.warn(`Unknown Twilio message event: ${messageType}`, { callSid });
                    console.log(`🔍 [${callSid}] Unknown message:`, JSON.stringify(data, null, 2));
                    console.log(`⚠️ [${callSid}] Received invalid message structure`);
            }
        } catch (error) {
            if (error instanceof SyntaxError) {
                console.log(`❌ [${callSid}] JSON parsing error for message:`, message);
                websocketLogger.error(`JSON parsing error for Twilio ${flowType} message`, error instanceof Error ? error : new Error(String(error)));
            } else {
                websocketLogger.error(`Error processing Twilio ${flowType} message`, error instanceof Error ? error : new Error(String(error)), callSid);
            }
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    ws.on('message', messageHandler);

    const closeHandler = async (code: number, reason: Buffer) => {
        websocketLogger.info(`Twilio ${flowType} connection closed`, {
            callSid,
            code,
            reason: reason ? reason.toString() : 'No reason',
            closeCode: code
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(callSid);

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // CRITICAL FIX: Don.js't end session immediately on WebSocket close
            // Twilio WebSocket can close/reconnect during normal call flow
            // Only end session if this is a deliberate call termination

            // Check if this is a normal close (1000) or abnormal close
            // Code 1005 means "no status received" and is common for Twilio WebSocket connections
            const isNormalClose = code === 1000 || code === 1005;
            const isCallStillActive = connectionData.isSessionActive && connectionData.geminiSession;

            // CRITICAL FIX: Don.js't end session on WebSocket close unless call is actually terminated
            // Twilio WebSocket can close while call is still active
            // Only end session if we received a .js'stop.js' message or call status indicates completion
            if (!isCallStillActive || connectionData.callCompleted || connectionData.stopReceived) {
                // Session already inactive or call explicitly ended - safe to end
                websocketLogger.info(`Ending session due to inactive session or explicit call termination`, {
                    callSid,
                    code,
                    isCallStillActive,
                    callCompleted: connectionData.callCompleted,
                    stopReceived: connectionData.stopReceived
                });
                if (lifecycleManager) {
                    await lifecycleManager.endSession(callSid, connectionData, 'twilio_connection_closed');
                } else {
                    endSession(callSid, deps, 'twilio_connection_closed');
                }
            } else {
                // WebSocket closed but session still active - don.js't end session
                websocketLogger.warn(`WebSocket closed but session still active - keeping session alive`, {
                    callSid,
                    code,
                    isCallStillActive
                });
                connectionData.wsDisconnected = true;
                connectionData.lastDisconnectTime = Date.now();

                // Don.js't end session - let call status webhook handle actual call termination
            }
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler as any);
        }
        eventListeners.clear();

        // CRITICAL: Clean up connection data to prevent stale state
        console.log(`🧹 [${callSid}] Cleaning up connection data to prevent stale state`);
        activeConnections.delete(callSid);
    };

    const errorHandler = async (error: Error) => {
        websocketLogger.error(`Twilio ${flowType} error`, error instanceof Error ? error : new Error(String(error)));

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Try recovery first if possible
            if (recoveryManager && contextManager.canRecover(callSid)) {
                websocketLogger.info('Twilio WebSocket error detected, attempting session recovery', { callSid });
                contextManager.markSessionInterrupted(callSid, 'twilio_websocket_error');
                setTimeout(async () => {
                    await recoveryManager.recoverSession(callSid, 'twilio_websocket_error', activeConnections);
                }, 1000);
            } else {
                // Mark connection as errored but don't immediately end session
                // Let the call status webhook or explicit user action end the session
                websocketLogger.warn('WebSocket error but no recovery available - marking connection as errored', { callSid });
                connectionData.wsError = true;
                connectionData.lastErrorTime = Date.now();

                // Clean up Deepgram transcription
                if (connectionData.deepgramConnection) {
                    deps.transcriptionManager.closeTranscription(callSid);
                }

                // Only end session if this is a critical error and session is not active
                if (!connectionData.isSessionActive || !connectionData.geminiSession) {
                    endSession(callSid, { ...deps, transcriptionManager: deps.transcriptionManager }, 'twilio_connection_error');
                }
            }
        }
    };

    // Register all event listeners
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

async function handleTwilioStartSession(
    callSid: string,
    data: TwilioStartMessage,
    deps: FlowDependencies,
    ws: WebSocket,
    flowType: string,
    isIncomingCall: boolean,
    getSessionConfig: () => any,
    activeConnections: Map<string, ConnectionData>,
    healthMonitor: any,
    lifecycleManager: any,
    sessionManager: any
): Promise<void> {
    websocketLogger.info(`Starting Twilio ${flowType} session`, { callSid });

    try {
        // Extract Twilio stream information
        const streamInfo = extractTwilioStreamInfo(data);

        // Get and validate session configuration
        const configResult = await getValidSessionConfig(callSid, getSessionConfig, deps, isIncomingCall);
        const sessionConfig = configResult.config;

        // Log stream information
        logTwilioStreamInfo(callSid, streamInfo, sessionConfig);

        // Create and store enhanced connection data
        const connectionData = createTwilioConnectionData(
            callSid, ws, streamInfo, sessionConfig, isIncomingCall, flowType
        );
        activeConnections.set(callSid, connectionData);

        // Track connection health
        healthMonitor.trackConnection(callSid, 'connected', {
            flowType,
            isTwilioCall: true,
            scriptId: sessionConfig.scriptId
        });

        // Create Gemini session with timeout handling
        console.log(`🔄 [${callSid}] Creating Gemini session...`);
        const sessionCreationTimeout = 30000; // 30 second timeout

        let geminiSession;
        try {
            geminiSession = await Promise.race([
                sessionManager.createGeminiSession(callSid, sessionConfig, connectionData),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Gemini session creation timeout')), sessionCreationTimeout)
                )
            ]);
        } catch (error) {
            websocketLogger.error(`Failed to create Gemini session for ${callSid}`, error instanceof Error ? error : new Error(String(error)));
            await handleGeminiSessionFailure(callSid, flowType, ws, deps, endSession);
            return;
        }

        if (geminiSession) {
            connectionData.geminiSession = geminiSession;
            console.log(`✅ [${callSid}] Gemini session created, waiting for activation...`);

            // CRITICAL: Wait for session to be properly activated before proceeding
            const maxWaitTime = 10000; // 10 seconds max wait
            const startWait = Date.now();

            while (!connectionData.isSessionActive && (Date.now() - startWait) < maxWaitTime) {
                await new Promise(resolve => setTimeout(resolve, 100)); // Wait 100ms
            }

            if (!connectionData.isSessionActive) {
                websocketLogger.error(`Gemini session failed to activate within ${maxWaitTime}ms for ${callSid}`);
                await handleGeminiSessionFailure(callSid, flowType, ws, deps, endSession);
                return;
            }

            console.log(`✅ [${callSid}] Gemini session activated successfully after ${Date.now() - startWait}ms`);

            // Start lifecycle management
            startLifecycleManagement(callSid, lifecycleManager, connectionData, sessionConfig);

            // Start WebSocket heartbeat monitoring
            startTwilioHeartbeat(callSid, ws, globalHeartbeatManager, activeConnections);

            // Live API setup is now handled by SessionManager during session creation
            websocketLogger.info(`Live API setup handled by SessionManager for ${flowType}`, { callSid });

            // Log session configuration
            logSessionConfiguration(callSid, sessionConfig, flowType);

            // Send session started message
            sendSessionStartedMessage(ws, callSid, flowType, sessionConfig.scriptId);

            websocketLogger.info(`Twilio ${flowType} session started successfully`, { callSid });
        } else {
            // Handle Gemini session creation failure
            await handleGeminiSessionFailure(callSid, flowType, ws, deps, endSession);
            return;
        }

    } catch (error) {
        handleSessionStartError(error, flowType, ws);
    }
}

async function handleTwilioMedia(
    callSid: string,
    data: TwilioMediaMessage,
    geminiSession: any,
    isSessionActive: boolean,
    deps: FlowDependencies,
    activeConnections: Map<string, ConnectionData>,
    lifecycleManager: any,
    recoveryManager: any
): Promise<void> {
    if (data.media && data.media.payload) {
        try {
            // Update activity for session persistence
            lifecycleManager.transitionState(callSid, 'active', 'media_received');

            // Get fresh connection data to check current session state
            const connectionData = activeConnections.get(callSid);

            // CRITICAL VALIDATION: Check connection state before processing audio
            if (!connectionData) {
                console.error(`❌ [${callSid}] No connection data found for media processing`);
                return;
            }

            if (!connectionData.geminiSession) {
                console.error(`❌ [${callSid}] No Gemini session for media processing`);
                return;
            }

            if (!connectionData.isSessionActive) {
                console.error(`❌ [${callSid}] Session not active for media processing`);
                return;
            }

            // Additional validation for session readiness
            if (connectionData.geminiSessionError) {
                console.error(`❌ [${callSid}] Gemini session has error: ${connectionData.geminiSessionError}`);
                return;
            }

            // Check if session is fully ready (both Twilio and Gemini connected)
            if (!connectionData.fullyReady && connectionData.twilioConnected !== undefined) {
                console.warn(`⚠️ [${callSid}] Session not fully ready - processing audio anyway (Twilio: ${connectionData.twilioConnected}, Gemini: ${connectionData.isSessionActive})`);
            }

            const currentIsSessionActive = connectionData.isSessionActive;
            const currentGeminiSession = connectionData.geminiSession;

            // Convert Twilio μ-law audio to PCM
            const ulawAudio = Buffer.from(data.media.payload, 'base64');

            // Final validation before sending audio
            if (deps.sessionManager && currentGeminiSession && currentIsSessionActive) {
                console.log(`🔍 [${callSid}] Sending audio to Gemini - session validated and active`);
                await deps.sessionManager.sendAudioToGemini(callSid, currentGeminiSession, ulawAudio);

                // Update last activity timestamp
                connectionData.lastActivity = Date.now();
            } else {
                console.log(`⚠️ [${callSid}] Skipping audio send - validation failed:`, {
                    hasSessionManager: !!deps.sessionManager,
                    hasGeminiSession: !!currentGeminiSession,
                    isSessionActive: currentIsSessionActive,
                    hasConnectionData: !!connectionData
                });
            }

        } catch (error) {
            websocketLogger.error(`Error processing Twilio media for ${callSid}`, error instanceof Error ? error : new Error(String(error)));

            // Mark connection as having media processing error
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                connectionData.lastMediaError = Date.now();
                connectionData.mediaErrorCount = (connectionData.mediaErrorCount || 0) + 1;
            }

            // Attempt recovery if needed
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(callSid, activeConnections)) {
                console.log(`🔄 [${callSid}] Attempting recovery due to media processing error`);
                await recoveryManager.recoverSession(callSid, 'media_processing_error', activeConnections);
            }
        }
    } else {
        console.warn(`⚠️ [${callSid}] Received media message without payload`);
    }
}

async function handleTwilioEndSession(
    callSid: string, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any
): Promise<void> {
    websocketLogger.info('Ending Twilio session - stop received', { callSid });

    const connectionData = activeConnections.get(callSid);
    if (connectionData) {
        // Mark that stop was received to prevent premature session termination on WebSocket close
        connectionData.stopReceived = true;

        if (lifecycleManager) {
            await lifecycleManager.endSession(callSid, connectionData, 'twilio_stop_received');
        } else {
            endSession(callSid, deps, 'twilio_stop_received');
        }
    }
}