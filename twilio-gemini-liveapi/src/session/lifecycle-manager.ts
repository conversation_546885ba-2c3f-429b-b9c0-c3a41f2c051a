// Session Lifecycle Manager - handles session lifecycle management and state transitions
import { ConnectionData } from '../types/global';
import { ContextManager } from './context-manager';
import { ConnectionHealthMonitor } from './health-monitor';
import { SessionSummaryManager } from './summary-manager';

interface LifecycleState {
    state: 'initializing' | 'active' | 'paused' | 'recovering' | 'ending' | 'ended' | 'error';
    timestamp: number;
    reason?: string;
    metadata?: Record<string, any>;
}

interface SessionLifecycleData {
    callSid: string;
    currentState: LifecycleState;
    stateHistory: LifecycleState[];
    startTime: number;
    endTime?: number;
    duration?: number;
    errorCount: number;
    transitionCount: number;
}

interface LifecycleTransition {
    from: LifecycleState['state'];
    to: LifecycleState['state'];
    timestamp: number;
    duration: number;
    reason?: string;
}

interface ExtendedConnectionData extends ConnectionData {
    // All properties are now inherited from ConnectionData
    twilioWs?: any;
    localWs?: any;
}

export class SessionLifecycleManager {
    private contextManager: ContextManager;
    private healthMonitor: ConnectionHealthMonitor;
    private summaryManager: SessionSummaryManager;
    private lifecycleData: Map<string, SessionLifecycleData>;
    private stateTransitionHandlers: Map<string, (data: SessionLifecycleData) => void>;
    private maxStateHistorySize: number;
    private sessionTimeouts: Map<string, NodeJS.Timeout>;

    constructor(contextManager: ContextManager, healthMonitor: ConnectionHealthMonitor, summaryManager: SessionSummaryManager) {
        this.contextManager = contextManager;
        this.healthMonitor = healthMonitor;
        this.summaryManager = summaryManager;
        this.lifecycleData = new Map();
        this.stateTransitionHandlers = new Map();
        this.maxStateHistorySize = 50; // Limit state history to prevent memory leaks
        this.sessionTimeouts = new Map();

        // Register default state transition handlers
        this.registerDefaultHandlers();
    }

    /**
     * Register default state transition handlers
     */
    private registerDefaultHandlers(): void {
        // Handler for session initialization
        this.stateTransitionHandlers.set('initializing', (data) => {
            console.log(`🔄 [${data.callSid}] Session initializing...`);
            this.healthMonitor.trackConnection(data.callSid, 'connecting');
        });

        // Handler for active sessions
        this.stateTransitionHandlers.set('active', (data) => {
            console.log(`✅ [${data.callSid}] Session active`);
            this.healthMonitor.trackConnection(data.callSid, 'connected');
        });

        // Handler for ending sessions
        this.stateTransitionHandlers.set('ending', (data) => {
            console.log(`🔚 [${data.callSid}] Session ending...`);
            this.healthMonitor.trackConnection(data.callSid, 'disconnected');
        });

        // Handler for error states
        this.stateTransitionHandlers.set('error', (data) => {
            console.log(`❌ [${data.callSid}] Session error: ${data.currentState.reason}`);
            this.healthMonitor.trackConnection(data.callSid, 'failed', {
                error: data.currentState.reason
            });
        });
    }

    /**
     * Initialize a new session
     * @param callSid - Call/session ID
     * @param initialMetadata - Initial metadata for the session
     */
    initializeSession(callSid: string, initialMetadata?: Record<string, any>): void {
        const now = Date.now();
        const initialState: LifecycleState = {
            state: 'initializing',
            timestamp: now,
            metadata: initialMetadata
        };

        const lifecycleData: SessionLifecycleData = {
            callSid,
            currentState: initialState,
            stateHistory: [initialState],
            startTime: now,
            errorCount: 0,
            transitionCount: 0
        };

        this.lifecycleData.set(callSid, lifecycleData);
        
        // Execute handler for initial state
        const handler = this.stateTransitionHandlers.get('initializing');
        if (handler) {
            handler(lifecycleData);
        }

        console.log(`🎯 [${callSid}] Session lifecycle initialized`);
    }

    /**
     * Transition session to a new state
     * @param callSid - Call/session ID
     * @param newState - New state to transition to
     * @param reason - Reason for the transition
     * @param metadata - Additional metadata for the transition
     */
    transitionState(callSid: string, newState: LifecycleState['state'], reason?: string, metadata?: Record<string, any>): boolean {
        const lifecycleData = this.lifecycleData.get(callSid);
        if (!lifecycleData) {
            console.warn(`⚠️ [${callSid}] No lifecycle data found for state transition`);
            return false;
        }

        const oldState = lifecycleData.currentState;
        const now = Date.now();

        // Validate state transition
        if (!this.isValidTransition(oldState.state, newState)) {
            console.warn(`⚠️ [${callSid}] Invalid state transition: ${oldState.state} -> ${newState}`);
            return false;
        }

        // Create new state
        const newStateData: LifecycleState = {
            state: newState,
            timestamp: now,
            reason,
            metadata
        };

        // Update lifecycle data
        lifecycleData.currentState = newStateData;
        lifecycleData.stateHistory.push(newStateData);
        lifecycleData.transitionCount++;

        // Maintain bounded history
        if (lifecycleData.stateHistory.length > this.maxStateHistorySize) {
            lifecycleData.stateHistory.shift();
        }

        // Track errors
        if (newState === 'error') {
            lifecycleData.errorCount++;
        }

        // Update end time if ending or ended
        if (newState === 'ended') {
            lifecycleData.endTime = now;
            lifecycleData.duration = now - lifecycleData.startTime;
        }

        // Execute state transition handler
        const handler = this.stateTransitionHandlers.get(newState);
        if (handler) {
            handler(lifecycleData);
        }

        // Log transition
        const transition: LifecycleTransition = {
            from: oldState.state,
            to: newState,
            timestamp: now,
            duration: now - oldState.timestamp,
            reason
        };
        console.log(`🔄 [${callSid}] State transition:`, transition);

        return true;
    }

    /**
     * Validate if a state transition is allowed
     * @param fromState - Current state
     * @param toState - Target state
     * @returns True if transition is valid
     */
    private isValidTransition(fromState: LifecycleState['state'], toState: LifecycleState['state']): boolean {
        const validTransitions: Record<LifecycleState['state'], LifecycleState['state'][]> = {
            'initializing': ['active', 'error', 'ended'],
            'active': ['paused', 'recovering', 'ending', 'error'],
            'paused': ['active', 'ending', 'error'],
            'recovering': ['active', 'ending', 'error'],
            'ending': ['ended', 'error'],
            'ended': [], // Terminal state
            'error': ['recovering', 'ending', 'ended']
        };

        return validTransitions[fromState]?.includes(toState) || false;
    }

    /**
     * End a session with proper cleanup
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param reason - Reason for ending the session
     */
    async endSession(callSid: string, connectionData: ConnectionData, reason?: string): Promise<void> {
        console.log(`🔚 [${callSid}] Ending session lifecycle (reason: ${reason || 'normal'})`);
        const extConnectionData = connectionData as ExtendedConnectionData;

        // Transition to ending state
        this.transitionState(callSid, 'ending', reason);

        try {
            // Request summary if not already requested
            if (!extConnectionData.summaryRequested) {
                console.log(`📋 [${callSid}] Requesting session summary before cleanup`);
                const summarySuccess = await this.summaryManager.requestSummary(callSid, connectionData, this.contextManager);

                if (summarySuccess) {
                    // Wait longer for summary to be generated (increased from 2s to 10s)
                    console.log(`⏳ [${callSid}] Waiting for summary generation (up to 10 seconds)`);
                    await this.waitForSummaryCompletion(callSid, extConnectionData, 10000);
                } else {
                    console.log(`⚠️ [${callSid}] Summary request failed, proceeding with cleanup`);
                }
            }

            // Clean up health monitoring
            this.healthMonitor.removeConnection(callSid);

            // Clean up context
            this.contextManager.clearSessionContext(callSid);

            // Clean up summary tracking
            this.summaryManager.cleanupSummary(callSid, connectionData);

            // Clear any session timeouts
            this.clearSessionTimeout(callSid);

            // Transition to ended state
            this.transitionState(callSid, 'ended', 'cleanup_complete');

            // Get final lifecycle data for logging
            const lifecycleData = this.lifecycleData.get(callSid);
            if (lifecycleData) {
                console.log(`📊 [${callSid}] Session lifecycle summary:`, {
                    duration: lifecycleData.duration ? `${Math.round(lifecycleData.duration / 1000)}s` : 'unknown',
                    stateTransitions: lifecycleData.transitionCount,
                    errorCount: lifecycleData.errorCount,
                    finalState: lifecycleData.currentState.state
                });
            }

            // Remove lifecycle data after a delay to allow for any final operations
            setTimeout(() => {
                this.lifecycleData.delete(callSid);
                console.log(`🗑️ [${callSid}] Lifecycle data removed`);
            }, 5000);

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session end:`, error);
            this.transitionState(callSid, 'error', 'cleanup_error');
        }
    }

    /**
     * Handle session timeout
     * @param callSid - Call/session ID
     * @param timeoutDuration - Timeout duration in milliseconds
     * @param timeoutHandler - Handler to execute on timeout
     */
    setSessionTimeout(callSid: string, timeoutDuration: number, timeoutHandler: () => void): void {
        // Clear any existing timeout
        this.clearSessionTimeout(callSid);

        // Set new timeout
        const timeoutId = setTimeout(() => {
            console.log(`⏰ [${callSid}] Session timeout triggered after ${timeoutDuration}ms`);
            this.sessionTimeouts.delete(callSid);
            timeoutHandler();
        }, timeoutDuration);

        this.sessionTimeouts.set(callSid, timeoutId);
        console.log(`⏱️ [${callSid}] Session timeout set for ${timeoutDuration}ms`);
    }

    /**
     * Clear session timeout
     * @param callSid - Call/session ID
     */
    clearSessionTimeout(callSid: string): void {
        const timeoutId = this.sessionTimeouts.get(callSid);
        if (timeoutId) {
            clearTimeout(timeoutId);
            this.sessionTimeouts.delete(callSid);
            console.log(`⏹️ [${callSid}] Session timeout cleared`);
        }
    }

    /**
     * Get current session state
     * @param callSid - Call/session ID
     * @returns Current lifecycle state or null
     */
    getCurrentState(callSid: string): LifecycleState | null {
        const lifecycleData = this.lifecycleData.get(callSid);
        return lifecycleData?.currentState || null;
    }

    /**
     * Get session lifecycle history
     * @param callSid - Call/session ID
     * @returns State history array
     */
    getStateHistory(callSid: string): LifecycleState[] {
        const lifecycleData = this.lifecycleData.get(callSid);
        return lifecycleData?.stateHistory || [];
    }

    /**
     * Get session lifecycle metrics
     * @param callSid - Call/session ID
     * @returns Lifecycle metrics
     */
    getLifecycleMetrics(callSid: string): {
        duration?: number;
        stateTransitions: number;
        errorCount: number;
        currentState?: string;
        isActive: boolean;
    } | null {
        const lifecycleData = this.lifecycleData.get(callSid);
        if (!lifecycleData) {return null;}

        return {
            duration: lifecycleData.duration || (Date.now() - lifecycleData.startTime),
            stateTransitions: lifecycleData.transitionCount,
            errorCount: lifecycleData.errorCount,
            currentState: lifecycleData.currentState.state,
            isActive: !['ended', 'error'].includes(lifecycleData.currentState.state)
        };
    }

    /**
     * Check if session is in a terminal state
     * @param callSid - Call/session ID
     * @returns True if session is ended or in error
     */
    isSessionTerminal(callSid: string): boolean {
        const state = this.getCurrentState(callSid);
        return state ? ['ended', 'error'].includes(state.state) : true;
    }

    /**
     * Register a custom state transition handler
     * @param state - State to handle
     * @param handler - Handler function
     */
    registerStateHandler(state: LifecycleState['state'], handler: (data: SessionLifecycleData) => void): void {
        this.stateTransitionHandlers.set(state, handler);
        console.log(`📝 Registered handler for state: ${state}`);
    }

    /**
     * Get all active sessions
     * @returns Array of active session IDs
     */
    getActiveSessions(): string[] {
        const activeSessions: string[] = [];
        for (const [callSid, data] of this.lifecycleData.entries()) {
            if (!['ended', 'error'].includes(data.currentState.state)) {
                activeSessions.push(callSid);
            }
        }
        return activeSessions;
    }

    /**
     * Wait for summary completion with timeout
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     * @param timeoutMs - Timeout in milliseconds
     */
    private async waitForSummaryCompletion(callSid: string, connectionData: ExtendedConnectionData, timeoutMs: number): Promise<void> {
        const startTime = Date.now();
        const checkInterval = 200; // Check every 200ms

        while (Date.now() - startTime < timeoutMs) {
            if (connectionData.summaryReceived) {
                console.log(`✅ [${callSid}] Summary completed in ${Date.now() - startTime}ms`);
                return;
            }

            // Check if we have partial summary text
            if (connectionData.summaryText && connectionData.summaryText.length > 0) {
                console.log(`📝 [${callSid}] Partial summary available (${connectionData.summaryText.length} chars)`);
            }

            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }

        // Timeout reached
        if (connectionData.summaryText && connectionData.summaryText.length > 0) {
            console.log(`⏰ [${callSid}] Summary timeout reached, but partial summary available`);
            connectionData.summaryReceived = true; // Mark as received to use partial summary
        } else {
            console.log(`⏰ [${callSid}] Summary timeout reached with no partial summary`);
        }
    }

    /**
     * Clean up all lifecycle data (for shutdown)
     */
    cleanup(): void {
        const sessionCount = this.lifecycleData.size;
        const timeoutCount = this.sessionTimeouts.size;

        // Clear all timeouts
        for (const [callSid, timeoutId] of this.sessionTimeouts.entries()) {
            clearTimeout(timeoutId);
        }

        this.lifecycleData.clear();
        this.sessionTimeouts.clear();
        this.stateTransitionHandlers.clear();

        console.log(`🧹 LifecycleManager: Cleared ${sessionCount} sessions and ${timeoutCount} timeouts`);
    }
}

// Export alias for backward compatibility
export { SessionLifecycleManager as LifecycleManager };