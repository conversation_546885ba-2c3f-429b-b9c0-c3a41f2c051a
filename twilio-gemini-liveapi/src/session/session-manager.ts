import { Modality } from '../gemini/client';
import { AudioProcessor } from '../audio/audio-processor';
import { forwardAudio, initializeAudioForwarding, cleanupAudioForwarding } from '../audio/audio-forwarding';
import { routeGeminiMessage } from './message-router';
import { generateLocalSummary } from './summary-generator';
import { standardizeConnectionData } from '../utils/websocket-utils';
import { timerManager } from '../utils/timer-manager';
import { sessionLogger } from '../utils/logger';
import {
    ConnectionData,
    GeminiSession,
    GeminiClient,
    GeminiLiveMessage,
    GeminiRealtimeInput,
    GeminiError,
    ConversationEntry,
    TranscriptEntry,
    SpeechTranscriptEntry
} from '../types/global';
import { ContextManager } from './context-manager';

interface SessionConfig {
    model: string;
    voice: string;
    aiInstructions?: string;
    sessionType?: string;
    isIncomingCall?: boolean;
    scriptId?: string | null;
    campaignId?: string | null;
    scriptType?: string;
    targetName?: string;
    targetPhoneNumber?: string;
}

interface SessionMetrics {
    startTime: number;
    messagesReceived: number;
    messagesSent: number;
    recoveryCount: number;
    lastActivity: number;
    isInitializing: boolean;
    lastRecoveryTime?: number;
}




interface ExtendedConnectionData extends ConnectionData {
    sessionConfig?: SessionConfig;
    sessionReady?: boolean;
    sessionInitialized?: number;
    geminiSessionError?: string;
    // Other properties are inherited from ConnectionData
    isTwilioCall?: boolean;
    streamSid?: string;
}

// Bounded Map and Set for memory safety
class BoundedMap<K, V> extends Map<K, V> {
    constructor(private maxSize: number = 1000) {
        super();
    }

    set(key: K, value: V): this {
        if (this.size >= this.maxSize && !this.has(key)) {
            const firstKey = this.keys().next().value;
            if (firstKey !== undefined) {
                this.delete(firstKey);
                sessionLogger.info(`🧹 SessionManager BoundedMap: Removed oldest entry ${firstKey}`);
            }
        }
        return super.set(key, value);
    }
}

class BoundedSet<T> extends Set<T> {
    constructor(private maxSize: number = 1000) {
        super();
    }

    add(value: T): this {
        if (this.size >= this.maxSize && !this.has(value)) {
            const firstValue = this.values().next().value;
            if (firstValue !== undefined) {
                this.delete(firstValue);
                sessionLogger.info(`🧹 SessionManager BoundedSet: Removed oldest entry ${firstValue}`);
            }
        }
        return super.add(value);
    }
}

// Session Manager for Gemini connections with recovery
export class SessionManager {
    private contextManager: ContextManager;
    private geminiClient: GeminiClient;
    private recoveryInProgress: BoundedSet<string>;
    private audioProcessor: AudioProcessor;
    private sessionMetrics: BoundedMap<string, SessionMetrics>;
    private activeConnections: Map<string, ConnectionData> | null;

    constructor(
        contextManager: ContextManager,
        geminiClient: GeminiClient,
        activeConnections: Map<string, ConnectionData> | null = null
    ) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new BoundedSet<string>(200); // Limit recovery operations
        this.audioProcessor = new AudioProcessor();
        this.sessionMetrics = new BoundedMap<string, SessionMetrics>(1000); // Limit session metrics
        this.activeConnections = activeConnections; // Reference to activeConnections for audio forwarding
    }

    // Utility function to maintain bounded arrays and prevent memory leaks
    private addToBoundedArray<T>(array: T[], item: T, maxSize: number): T[] {
        if (!Array.isArray(array)) {
            sessionLogger.warn('addToBoundedArray: array is not an array, initializing as empty array');
            array = [];
        }

        array.push(item);

        // Remove oldest entries if limit exceeded
        if (array.length > maxSize) {
            const removed = array.splice(0, array.length - maxSize);
            sessionLogger.info(`🧹 Trimmed ${removed.length} old entries from bounded array (max: ${maxSize})`);
        }

        return array;
    }

    // Create new Gemini session
    async createGeminiSession(
        callSid: string,
        config: SessionConfig,
        connectionData: ConnectionData
    ): Promise<GeminiSession | null> {
        const sessionStartTime = Date.now();
        const extConnectionData = connectionData as ExtendedConnectionData;
        
        try {
            sessionLogger.info(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);
            sessionLogger.info(`🔍 [${callSid}] ===== SESSION MANAGER MODEL DEBUG =====`);
            sessionLogger.info(`🔍 [${callSid}] config.model = "${config.model}"`);
            sessionLogger.info(`🔍 [${callSid}] config.voice = "${config.voice}"`);
            sessionLogger.info(`🔍 [${callSid}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
            sessionLogger.info(`🔍 [${callSid}] ==========================================`);

            // CRITICAL VALIDATION: Verify AI instructions are present before creating session
            if (!config.aiInstructions || config.aiInstructions.trim().length === 0) {
                const errorMessage = `Cannot create Gemini session: AI instructions are missing or empty`;
                sessionLogger.error(`❌ [${callSid}] VALIDATION FAILED: ${errorMessage}`);
                sessionLogger.error(`❌ [${callSid}] Session config validation:`, {
                    hasConfig: !!config,
                    hasAiInstructions: !!config.aiInstructions,
                    instructionLength: config.aiInstructions?.length || 0,
                    sessionType: config.sessionType,
                    scriptId: config.scriptId
                });
                
                // Mark session as failed and return early
                extConnectionData.isSessionActive = false;
                extConnectionData.geminiSessionError = errorMessage;
                
                throw new Error(errorMessage);
            }
            
            sessionLogger.info(`✅ [${callSid}] AI instructions validation passed (${config.aiInstructions.length} characters)`);
            const validationPreview = config.aiInstructions.substring(0, 200);
            sessionLogger.info(`🎯 [${callSid}] Instructions preview: ${validationPreview}...`);

            // Use arrow functions to maintain 'this' context instead of aliasing

            // Initialize session metrics BEFORE creating session to avoid race conditions
            this.sessionMetrics.set(callSid, {
                startTime: Date.now(),
                messagesReceived: 0,
                messagesSent: 0,
                recoveryCount: 0,
                lastActivity: Date.now(),
                isInitializing: true
            });

            // Store reference immediately to prevent race conditions
            extConnectionData.geminiSession = undefined;
            extConnectionData.isSessionActive = false;

            // Store config for callback access
            extConnectionData.sessionConfig = config;

            // Create session and store reference immediately
            // Add call direction context to system instructions
            let systemInstruction = config.aiInstructions;
            sessionLogger.info(`🎯 [${callSid}] ===== SYSTEM INSTRUCTION DEBUG =====`);
            sessionLogger.info(`🎯 [${callSid}] Has AI instructions: ${!!config.aiInstructions}`);
            sessionLogger.info(`🎯 [${callSid}] Instruction length: ${systemInstruction?.length || 0}`);
            sessionLogger.info(`🎯 [${callSid}] Script ID: ${config.scriptId}`);
            sessionLogger.info(`🎯 [${callSid}] Campaign ID: ${config.campaignId}`);
            sessionLogger.info(`🎯 [${callSid}] Script type: ${config.scriptType}`);
            const instructionPreview = systemInstruction?.substring(0, 200) || 'NO INSTRUCTIONS';
            sessionLogger.info(`🎯 [${callSid}] First 200 chars: ${instructionPreview}...`);
            sessionLogger.info(`🎯 [${callSid}] ====================================`);

            // Add context based on call direction for Twilio calls
            if (config.sessionType === 'twilio_call' && systemInstruction) {
                if (!config.isIncomingCall) {
                    // Outbound calls: AI initiates the conversation
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are making an outbound call. When the call connects, introduce yourself and ' +
                        'begin the conversation according to the script above. Start speaking immediately when the call is answered.';
                } else {
                    // Inbound calls: AI should greet the caller and wait for their response
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are receiving an inbound call. When the customer speaks, greet them warmly and ' +
                        'assist them according to the script above.';
                }
            }

            // Fallback for inbound calls without instructions
            if (!systemInstruction && config.isIncomingCall && config.sessionType === 'twilio_call') {
                sessionLogger.warn(`No AI instructions found for inbound call, using fallback`);
                systemInstruction = 'You are a helpful customer service representative. ' +
                    'Greet the caller warmly and ask how you can help them today.';
            }

            // CRITICAL DEBUG: Log full configuration being sent to Gemini
            sessionLogger.info(`🔍 [${callSid}] Attempting Gemini connection with config:`, {
                model: config.model,
                voice: config.voice,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall,
                hasSystemInstruction: !!systemInstruction,
                instructionLength: systemInstruction?.length,
                hasGeminiClient: !!this.geminiClient,
                hasLiveAPI: !!(this.geminiClient as any)?.live
            });

            if (systemInstruction) {
                sessionLogger.info(`🎯 [${callSid}] System instruction preview: ${systemInstruction.substring(0, 300)}...`);
            } else {
                sessionLogger.error(`❌ [${callSid}] CRITICAL: No system instruction available!`);
            }

            sessionLogger.info(`🚀 [${callSid}] Connecting to Gemini Live API with system instruction...`);
            const geminiSession = await (this.geminiClient as any).live.connect({
                model: config.model,
                systemInstruction: systemInstruction ? {
                    parts: [{
                        text: systemInstruction
                    }]
                } : undefined,
                generationConfig: {
                    responseModalities: ['AUDIO'],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                callbacks: {
                    onopen: () => {
                        sessionLogger.info(`✅ [${callSid}] Gemini session opened`);

                        // CRITICAL: Don't immediately mark as active - validate first
                        sessionLogger.info(`🔄 [${callSid}] Validating session before activation...`);

                        // Update metrics state
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.isInitializing = false;
                        }

                        // Save initial context with bounded arrays
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...extConnectionData,
                            conversationLog: [],
                            fullTranscript: [],
                            speechTranscript: [], // Initialize bounded speech transcript
                            maxConversationLogSize: 500,
                            maxTranscriptSize: 1000,
                            maxSpeechTranscriptSize: 1000 // Add bounds for speech transcript
                        });

                        // CRITICAL FIX: Live API setup is now handled in initial connection configuration
                        sessionLogger.info(`✅ [${callSid}] Live API setup configuration included in connection`);
                        if (config.aiInstructions) {
                            const instructionSnippet = config.aiInstructions.substring(0, 200);
                            sessionLogger.info(`🎯 [${callSid}] AI instructions included: ${instructionSnippet}...`);
                        } else {
                            sessionLogger.warn(`⚠️ [${callSid}] No AI instructions in config - session may not work properly`);
                        }

                        // Validate session setup
                        sessionLogger.info(`🔍 [${callSid}] Session validation:`, {
                            hasSystemInstruction: !!systemInstruction,
                            instructionLength: systemInstruction?.length,
                            model: config.model,
                            voice: config.voice,
                            hasGeminiSession: !!extConnectionData.geminiSession
                        });

                        // CRITICAL: Only mark as active after validation and small delay
                        setTimeout(() => {
                            if (extConnectionData.geminiSession && !extConnectionData.geminiSessionError) {
                                extConnectionData.isSessionActive = true;
                                extConnectionData.sessionActivatedAt = Date.now();

                                // Check if Twilio is also connected for full readiness
                                if (extConnectionData.twilioConnected) {
                                    extConnectionData.fullyReady = true;
                                    sessionLogger.info(`🎯 [${callSid}] Session fully ready - both Gemini and Twilio connected`);
                                } else {
                                    sessionLogger.info(`🔄 [${callSid}] Gemini session active, waiting for Twilio connection`);
                                }

                                sessionLogger.info(`✅ [${callSid}] Session activated and ready for audio processing`);
                            } else {
                                sessionLogger.error(`❌ [${callSid}] Session validation failed during activation`);
                                extConnectionData.geminiSessionError = 'Session validation failed';
                            }
                        }, 500); // 500ms delay to ensure session is stable

                        sessionLogger.info(`🔄 [${callSid}] Session initialized, activation pending validation`);
                    },

                    onerror: (error: GeminiError) => {
                        sessionLogger.error(`❌ [${callSid}] Gemini session error:`, error instanceof Error ? error : new Error(String(error)));
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }

                        // Mark session as inactive but don't end it - let recovery manager handle it
                        extConnectionData.isSessionActive = false;
                        extConnectionData.geminiSessionError = error.message;

                        sessionLogger.info(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },

                    onclose: (event: CloseEvent) => {
                        const sessionDuration = Date.now() - sessionStartTime;
                        sessionLogger.info(`🔌 [${callSid}] Gemini session closed after ${sessionDuration}ms`);
                        sessionLogger.info(`🔌 [${callSid}] Close event details:`, {
                            code: event?.code,
                            reason: event?.reason,
                            wasClean: event?.wasClean,
                            sessionDuration: sessionDuration
                        });

                        extConnectionData.isSessionActive = false;

                        // Check for early session termination (likely initialization failure)
                        if (sessionDuration < 5000) {
                            sessionLogger.error(
                                `❌ [${callSid}] Session closed too quickly (${sessionDuration}ms) - likely initialization failure`
                            );
                            sessionLogger.error(`❌ [${callSid}] This suggests API key, model, or configuration issues`);
                        }

                        // Session closed - cleanup handled by lifecycle manager

                        // Check if this is an unexpected close (connection still active)
                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = extConnectionData.ws && extConnectionData.ws.readyState === 1;

                        if (isUnexpectedClose) {
                            sessionLogger.info(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');

                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            sessionLogger.info(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        } else {
                            sessionLogger.info(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },

                    onmessage: async (message: GeminiLiveMessage) => {
                        await routeGeminiMessage(callSid, message, extConnectionData, {
                            audioProcessor: this.audioProcessor,
                            activeConnections: this.activeConnections || undefined,
                            sessionMetrics: this.sessionMetrics,
                            forwardAudioFn: forwardAudio
                        });
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });

            // REMOVED: AI instruction sending from session manager to prevent double instruction sending
            // Instructions will be sent by the appropriate flow handler (local-testing-handler.js or twilio-flow-handler.js)
            // This prevents race conditions and early session termination
            sessionLogger.info(`📝 [${callSid}] Session created - instructions will be sent by flow handler to prevent double sending`);
            if (config.aiInstructions) {
                sessionLogger.info(`📝 [${callSid}] AI instructions available (${config.aiInstructions.length} chars) - will be sent by handler`);
            } else {
                sessionLogger.warn(`⚠️ [${callSid}] No AI instructions available - handler should provide instructions`);
            }

            // Store the session in connectionData immediately after creation
            // This prevents race conditions where callbacks might execute before assignment
            extConnectionData.geminiSession = geminiSession as GeminiSession;

            // Mark session as ready for audio processing
            extConnectionData.sessionReady = true;
            extConnectionData.sessionInitialized = Date.now();

            // Initialize audio forwarding for this session
            initializeAudioForwarding(callSid, extConnectionData, config.sessionType || 'unknown');

            // Verify session was created successfully
            if (!geminiSession) {
                throw new Error('Gemini session creation returned null/undefined');
            }

            sessionLogger.info(`✅ [${callSid}] Gemini session created successfully and ready for audio`);

            // Add session health check after 5 seconds using timer manager
            timerManager.setTimeout(`${callSid}_health_check`, () => {
                if (extConnectionData.isSessionActive) {
                    sessionLogger.info(`✅ [${callSid}] Session health check: still active after 5 seconds`);
                } else {
                    sessionLogger.error(`❌ [${callSid}] Session health check: died within 5 seconds - check logs above for errors`);
                }
            }, 5000);

            return geminiSession as GeminiSession;

        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            sessionLogger.error(`❌ [${callSid}] Failed to create Gemini session:`, error instanceof Error ? error : new Error(String(error)));
            sessionLogger.error(`❌ [${callSid}] Error details:`, {
                message: errorMessage,
                code: (error as any)?.code,
                details: (error as any)?.details,
                stack: errorStack?.split('\n').slice(0, 5).join('\n') // First 5 lines of stack
            });

            // Log configuration that failed
            sessionLogger.error(`❌ [${callSid}] Failed configuration:`, {
                model: config.model,
                voice: config.voice,
                hasInstructions: !!config.aiInstructions,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall
            });

            // Mark session as failed
            extConnectionData.isSessionActive = false;
            extConnectionData.geminiSessionError = errorMessage;

            return null;
        }
    }

    // Note: Message handling is now done directly in the onmessage callback (like old implementation)

    // REMOVED: sendInitialMessage method - now using Live API setup configuration
    // Initial instructions are sent via setup message in session creation, not as client content

    // Legacy method for backward compatibility - Live API handles continuous conversation automatically
    async sendTextToGemini(sessionId: string, geminiSession: GeminiSession, text: string): Promise<void> {
        sessionLogger.info(`⚠️ [${sessionId}] sendTextToGemini called but not needed in Live API - text: ${text.substring(0, 100)}...`);
        // In Live API, text should be sent as audio through sendRealtimeInput
        // This is a no-op for backward compatibility
    }

    // Legacy method for backward compatibility - Live API handles turn management automatically
    async sendTurnComplete(sessionId: string, geminiSession: GeminiSession): Promise<void> {
        sessionLogger.info(`⚠️ [${sessionId}] sendTurnComplete called but not needed in Live API`);
        // Voice Activity Detection (VAD) in the Live API automatically manages conversation turns
        // This is a no-op for backward compatibility
    }

    // Send audio to Gemini session (for Twilio calls with μ-law audio)
    async sendAudioToGemini(callSid: string, geminiSession: GeminiSession, audioBuffer: Buffer): Promise<void> {
        try {
            sessionLogger.info(`🔍 [${callSid}] sendAudioToGemini called - geminiSession: ${!!geminiSession}, audioBuffer: ${!!audioBuffer}, audioSize: ${audioBuffer?.length || 0}`);

            if (!geminiSession || !audioBuffer) {
                sessionLogger.info(`⚠️ [${callSid}] sendAudioToGemini early return - missing geminiSession or audioBuffer`);
                return;
            }

            // Check if session is ready for audio processing
            const connectionData = this.activeConnections?.get(callSid) as ExtendedConnectionData;
            if (!connectionData?.sessionReady) {
                sessionLogger.info(`⚠️ [${callSid}] Session not ready for audio processing yet`);
                return;
            }

            sessionLogger.info(`🔍 [${callSid}] Updating metrics...`);
            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            sessionLogger.info(`🔍 [${callSid}] Converting audio format with enhanced validation - audioProcessor exists: ${!!this.audioProcessor}`);
            // Convert Twilio audio to Gemini format with enhanced validation
            const pcmBuffer = this.audioProcessor.convertUlawToPCM(audioBuffer, false, callSid);
            sessionLogger.info(`🔍 [${callSid}] PCM conversion complete - buffer size: ${pcmBuffer.length}`);

            if (pcmBuffer.length === 0) {
                sessionLogger.warn(`⚠️ [${callSid}] PCM conversion resulted in empty buffer - skipping audio send`);
                return;
            }

            const float32Data = this.audioProcessor.pcmToFloat32Array(pcmBuffer, callSid);
            sessionLogger.info(`🔍 [${callSid}] Float32 conversion complete - array length: ${float32Data.length}`);

            if (float32Data.length === 0) {
                sessionLogger.warn(`⚠️ [${callSid}] Float32 conversion resulted in empty array - skipping audio send`);
                return;
            }

            const audioBlob = this.audioProcessor.createGeminiAudioBlob(float32Data);
            sessionLogger.info(`🔍 [${callSid}] Audio blob created - size: ${audioBlob.data?.length || 'N/A'}`);

            sessionLogger.info(`🔍 [${callSid}] Sending audio to Gemini session...`);
            // Send to Gemini using sendRealtimeInput (like old implementation)
            await geminiSession.sendRealtimeInput({
                media: {
                    mimeType: audioBlob.mimeType,
                    data: audioBlob.data
                }
            });
            sessionLogger.info(`✅ [${callSid}] Audio sent to Gemini successfully`);

        } catch (error) {
            sessionLogger.error(`❌ [${callSid}] Error sending audio to Gemini:`, error instanceof Error ? error : new Error(String(error)));
        }
    }

    // Send browser PCM audio to Gemini session (for local testing)
    async sendBrowserAudioToGemini(callSid: string, geminiSession: GeminiSession, base64Audio: string): Promise<void> {
        try {
            sessionLogger.info(`🔍 [${callSid}] sendBrowserAudioToGemini called - geminiSession: ${!!geminiSession}, audioSize: ${base64Audio?.length || 0}`);

            if (!geminiSession || !base64Audio) {
                sessionLogger.info(`⚠️ [${callSid}] sendBrowserAudioToGemini early return - missing geminiSession or audio`);
                return;
            }

            // Check if session is ready for audio processing
            const connectionData = this.activeConnections?.get(callSid) as ExtendedConnectionData;
            if (!connectionData?.sessionReady) {
                sessionLogger.info(`⚠️ [${callSid}] Session not ready for audio processing yet`);
                return;
            }

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent = (metrics.messagesSent || 0) + 1;
                metrics.lastActivity = Date.now();
            }

            // MATCH OLD IMPLEMENTATION: Send audio exactly as received
            sessionLogger.info(`🎵 [${callSid}] Sending browser audio to Gemini (matching old implementation)...`);
            
            try {
                // Send audio EXACTLY like old implementation - just pass the media object
                await geminiSession.sendRealtimeInput({
                    media: {
                        data: base64Audio,
                        mimeType: 'audio/pcm;rate=16000'  // Browser sends PCM16
                    }
                });
                sessionLogger.info(`✅ [${callSid}] Browser audio sent to Gemini successfully`);
                
                // Add activity timestamp to track continuous flow
                const currentTime = Date.now();
                sessionLogger.info(`⏱️ [${callSid}] Audio sent at: ${currentTime}, session active: ${!!geminiSession}`);
                
            } catch (sendError: unknown) {
                sessionLogger.error(`🚨 [${callSid}] GEMINI AUDIO SEND ERROR 🚨`);
                sessionLogger.error(`❌ [${callSid}] Error sending browser audio directly:`, sendError instanceof Error ? sendError : new Error(String(sendError)));
                const errorMessage = sendError instanceof Error ? sendError.message : 'No message';
                sessionLogger.error(`❌ [${callSid}] Error message: ${errorMessage}`);

                // Check for quota errors
                if (errorMessage.includes('quota') || errorMessage.includes('exceeded')) {
                    sessionLogger.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN AUDIO SEND 🚨🚨🚨`);
                    sessionLogger.error(`💳 [${callSid}] Check your Gemini API billing and quota limits!`);
                }
                
                // Try with WebM mime type as fallback
                try {
                    await geminiSession.sendRealtimeInput({
                        media: {
                            data: base64Audio,
                            mimeType: 'audio/webm'
                        }
                    });
                    sessionLogger.info(`⚠️ [${callSid}] Browser audio sent with WebM mime type (fallback)`);
                } catch (fallbackError: unknown) {
                    sessionLogger.error(`🚨 [${callSid}] FALLBACK AUDIO SEND ALSO FAILED 🚨`);
                    sessionLogger.error(`❌ [${callSid}] All audio sending methods failed:`, fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError)));
                    const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : 'No message';
                    sessionLogger.error(`❌ [${callSid}] Fallback error message: ${fallbackErrorMessage}`);

                    if (fallbackErrorMessage.includes('quota') || fallbackErrorMessage.includes('exceeded')) {
                        sessionLogger.error(`🚨🚨🚨 [${callSid}] QUOTA EXCEEDED IN FALLBACK TOO 🚨🚨🚨`);
                    }
                }
            }

        } catch (error) {
            sessionLogger.error(`❌ [${callSid}] Error sending browser audio to Gemini:`, error instanceof Error ? error : new Error(String(error)));
        }
    }

    // Recover session after interruption
    async recoverSession(callSid: string, reason: string): Promise<void> {
        // Atomic check and set to prevent concurrent recovery attempts
        const wasAlreadyRecovering = this.recoveryInProgress.has(callSid);
        if (wasAlreadyRecovering) {
            sessionLogger.info(`⏳ [${callSid}] Recovery already in progress`);
            return;
        }
        
        // Immediately add to recovery set before any async operations
        this.recoveryInProgress.add(callSid);
        
        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                sessionLogger.info(`❌ [${callSid}] Cannot recover session`);
                return;
            }

            // Get connection data from active connections (would need to be passed in)
            // This is a simplified version - in practice you'd need access to activeConnections
            const recoveryCount = this.contextManager.incrementRecoveryAttempt(callSid);
            sessionLogger.info(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);

            // Update metrics with null check
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.recoveryCount = (metrics.recoveryCount || 0) + 1;
                metrics.lastRecoveryTime = Date.now();
            }

            // The actual recovery would happen in the main connection handler
            // This method primarily handles the recovery logic and context preparation
            
            sessionLogger.info(`✅ [${callSid}] Recovery preparation completed`);

        } catch (error) {
            sessionLogger.error(`❌ [${callSid}] Error during session recovery:`, error instanceof Error ? error : new Error(String(error)));
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }

    // Generate session summary
    async generateSummary(callSid: string, connectionData: ExtendedConnectionData, summaryPrompt: string): Promise<boolean> {
        try {
            sessionLogger.info(`📋 [${callSid}] Generating call summary`);
            
            if (!connectionData?.geminiSession) {
                sessionLogger.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return false;
            }

            connectionData.summaryRequested = true;
            connectionData.summaryText = '';

            // ALTERNATIVE APPROACH: For Live API, we can't easily get text-only summaries
            // Instead, we'll generate a summary from the conversation log we've been tracking
            // This is more reliable and doesn't interfere with the continuous conversation

            sessionLogger.info(`📝 [${callSid}] Generating summary from conversation log instead of requesting from AI`);

            // Generate summary from conversation log
            const conversationLog = connectionData.conversationLog || [];
            if (conversationLog.length > 0) {
                const summaryText = generateLocalSummary(conversationLog, summaryPrompt);
                connectionData.summaryText = summaryText;
                sessionLogger.info(`✅ [${callSid}] Local summary generated: ${summaryText.substring(0, 100)}...`);
            } else {
                connectionData.summaryText = 'No conversation content available for summary.';
                sessionLogger.info(`⚠️ [${callSid}] No conversation log available for summary`);
            }

            // Summary will be collected in the onmessage callback
            return true;

        } catch (error) {
            sessionLogger.error(`❌ [${callSid}] Error generating summary:`, error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }

    // Get session metrics
    getSessionMetrics(callSid: string): SessionMetrics | null {
        return this.sessionMetrics.get(callSid) || null;
    }

    // Clean up session
    cleanupSession(callSid: string): void {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        // Clear all timers for this session
        timerManager.clearSessionTimers(callSid);
        sessionLogger.info(`🧹 [${callSid}] Session manager cleanup completed`);
    }

}