// Script Management System for both incoming and outbound call scripts
// Now uses universal campaign script loader for real scripts from public directory
import { config, getConfigValue } from '../config/config';
import { ScriptCache } from './script-cache';
import { logger } from '../utils/logger';
import { convertIncomingScenarioToCampaignScript } from './incoming-converter';
import { CampaignScript, ScriptConfig, ScriptInfo, CustomScriptData } from './campaign-types';

// Import universal campaign script loader
import {
  loadCampaignScript,
  getAllCampaigns,
  formatCampaignScript,
  getIncomingCallScript as getOutboundCallScript,
  listIncomingCallScripts as listOutboundCallScripts,
  setIncomingCallScript as setOutboundCallScript,
  getCurrentIncomingScript as getCurrentOutboundScript,
  createCustomIncomingScript as createCustomOutboundScript,
  // Incoming scenario functions (now using real campaign scripts)
  getIncomingScenario,
  listIncomingScenarios,
  setActiveIncomingScenario,
  getCurrentIncomingScenario
} from './campaign-loader';

export class ScriptManager {
  private currentIncomingScript: string;
  private currentOutboundScript: string;
  private customScripts: Map<string, CustomScriptData>;
  private scriptCache: ScriptCache<CampaignScript>;
  private isPreloading: boolean;
  private loadingScripts: Set<string>;
  private fallbackInstructions: string;

  constructor() {
    this.currentIncomingScript = 'customer-service';
    this.currentOutboundScript = 'default';
    this.customScripts = new Map();
    this.scriptCache = new ScriptCache();
    this.isPreloading = false;
    this.loadingScripts = new Set();

    // Fallback AI instructions when script loading fails
    this.fallbackInstructions = `
AGENT PERSONA:
You are a professional AI assistant conducting a phone conversation. You should:
- Be polite, professional, and helpful
- Listen actively and respond appropriately
- Keep responses concise and natural
- Ask clarifying questions when needed
- Maintain a conversational tone

CONVERSATION GUIDELINES:
- Introduce yourself at the beginning of the call
- Be patient and understanding
- Provide clear and accurate information
- End the conversation politely when appropriate

TECHNICAL NOTES:
- This is a real-time voice conversation
- Respond naturally as if speaking to a person
- Keep responses under 30 seconds when possible
- Use natural speech patterns and pauses
`.trim();
  }

  async preloadScripts(): Promise<void> {
    if (this.isPreloading) {return;}
    this.isPreloading = true;
    const start = Date.now();
    try {
      for (let i = 1; i <= 6; i++) {
        const outbound = loadCampaignScript(i, 'outbound');
        if (outbound) {
          this.scriptCache.set(`outbound-${i}`, outbound);
        }
        const incoming = loadCampaignScript(i, 'incoming');
        if (incoming) {
          this.scriptCache.set(`incoming-${i}`, incoming);
        }
      }
      logger.info(`📋 Script preloading completed`, {
        count: this.scriptCache.size(),
        duration_ms: Date.now() - start
      });
    } catch (error) {
      logger.error('❌ Error preloading scripts', error instanceof Error ? error : new Error(String(error)));
    } finally {
      this.isPreloading = false;
    }
  }

  getScriptFromCache(id: string | number, type: 'outbound' | 'incoming' = 'outbound'): CampaignScript | null {
    return this.scriptCache.get(`${type}-${id}`);
  }

  // === INCOMING CALL SCRIPTS ===
  
  /**
   * Get all available incoming call scripts
   */
  getIncomingScripts(): ScriptInfo[] {
    try {
      const scenarios = listIncomingScenarios();
      return scenarios.map(scenario => ({
        id: scenario.id,
        name: scenario.name,
        description: scenario.description,
        type: 'incoming' as const,
        category: scenario.category || 'support'
      }));
    } catch (error) {
      console.error('Error getting incoming scripts:', error);
      return [];
    }
  }

  /**
   * Get current active incoming script
   */
  getCurrentIncomingScript(): string | null {
    try {
      return getCurrentIncomingScenario();
    } catch (error) {
      console.error('Error getting current incoming script:', error);
      return null;
    }
  }

  /**
   * Set active incoming script
   */
  setIncomingScript(scriptId: string): boolean {
    try {
      return setActiveIncomingScenario(scriptId);
    } catch (error) {
      console.error('Error setting incoming script:', error);
      return false;
    }
  }

  /**
   * Create custom incoming script
   */
  createCustomIncomingScript(scriptData: CustomScriptData): boolean {
    try {
      // No custom scenarios - we use 6 outbound + 6 inbound ready-made scripts
      console.log('Custom incoming scripts not supported - use campaigns 1-6 (incoming)');
      return false;
    } catch (error) {
      console.error('Error creating custom incoming script:', error);
      return false;
    }
  }

  // === OUTBOUND CALL SCRIPTS ===
  
  /**
   * Get all available outbound call scripts
   */
  getOutboundScripts(): ScriptInfo[] {
    try {
      return listOutboundCallScripts();
    } catch (error) {
      console.error('Error getting outbound scripts:', error);
      return [];
    }
  }

  /**
   * Get current active outbound script
   */
  getCurrentOutboundScript(): string | null {
    try {
      return getCurrentOutboundScript();
    } catch (error) {
      console.error('Error getting current outbound script:', error);
      return null;
    }
  }

  /**
   * Set active outbound script
   */
  setOutboundScript(scriptId: string): boolean {
    try {
      return setOutboundCallScript(scriptId);
    } catch (error) {
      console.error('Error setting outbound script:', error);
      return false;
    }
  }

  /**
   * Create custom outbound script
   */
  createCustomOutboundScript(scriptData: CustomScriptData): boolean {
    try {
      return createCustomOutboundScript(scriptData);
    } catch (error) {
      console.error('Error creating custom outbound script:', error);
      return false;
    }
  }

  // === CAMPAIGN SCRIPT CONVERSION ===
  
  /**
   * Get script configuration for session
   */
  async getScriptConfig(scriptId: string | number, isIncoming: boolean = false): Promise<ScriptConfig | null> {
    try {
      console.log(`📋 [SCRIPT-MANAGER] Loading script config:`, {
        scriptId,
        isIncoming,
        type: typeof scriptId
      });
      
      // UNIFIED SCRIPT LOADING - One simple approach
      const numericId = parseInt(scriptId.toString());
      let campaignScript: CampaignScript | null = null;
      let campaignId = numericId;
      let scriptType: 'incoming' | 'outbound' = isIncoming ? 'incoming' : 'outbound';

      // Handle unified ID system: 1-6 = outbound, 7-12 = incoming
      if (numericId >= 7 && numericId <= 12) {
        campaignId = numericId - 6; // 7->1, 8->2, ..., 12->6
        scriptType = 'incoming';
        console.log(`📋 [SCRIPT-MANAGER] Converting ID ${numericId} to incoming campaign ${campaignId}`);
      } else if (numericId >= 1 && numericId <= 6) {
        campaignId = numericId;
        scriptType = 'outbound';
        console.log(`📋 [SCRIPT-MANAGER] Using outbound campaign ${campaignId}`);
      } else {
        // For non-numeric IDs, use the isIncoming parameter
        campaignId = 1; // Default to campaign 1
        console.warn(`⚠️ [SCRIPT-MANAGER] Non-numeric ID '${scriptId}', defaulting to campaign 1`);
      }

      // Try to get from cache first
      campaignScript = this.getScriptFromCache(campaignId, scriptType);
      if (campaignScript) {
        console.log(`✅ [SCRIPT-MANAGER] Found script in cache: ${scriptType}-${campaignId}`);
      }

      // If not in cache, load it with race condition protection
      if (!campaignScript) {
        const loadKey = `${scriptType}-${campaignId}`;

        // Check if already loading to prevent race conditions
        if (this.loadingScripts.has(loadKey)) {
          console.log(`⏳ [SCRIPT-MANAGER] Script ${loadKey} already loading, waiting...`);
          // Wait a bit and try cache again
          await new Promise(resolve => setTimeout(resolve, 100));
          campaignScript = this.getScriptFromCache(campaignId, scriptType);
          if (campaignScript) {
            console.log(`✅ [SCRIPT-MANAGER] Found script in cache after wait: ${loadKey}`);
          }
        }

        // If still not found and not loading, load it
        if (!campaignScript && !this.loadingScripts.has(loadKey)) {
          this.loadingScripts.add(loadKey);
          try {
            console.log(`📋 [SCRIPT-MANAGER] Loading script from disk: ${scriptType} campaign ${campaignId}`);
            campaignScript = loadCampaignScript(campaignId, scriptType, false);
            if (campaignScript) {
              console.log(`✅ [SCRIPT-MANAGER] Loaded script successfully, caching...`);
              this.scriptCache.set(`${scriptType}-${campaignId}`, campaignScript);
            } else {
              console.error(`❌ [SCRIPT-MANAGER] Failed to load campaign script`);
            }
          } finally {
            this.loadingScripts.delete(loadKey);
          }
        }
      }

      // If we have a campaign script, return the config
      if (campaignScript) {
        console.log(`📋 [SCRIPT-MANAGER] Campaign title: ${campaignScript.campaign_title || campaignScript.title || 'Untitled'}`);
        console.log(`📋 [SCRIPT-MANAGER] Campaign type: ${campaignScript.type}, ID: ${campaignScript.id}`);
        console.log(`📋 [SCRIPT-MANAGER] Has agentPersona: ${!!campaignScript.agentPersona}`);
        console.log(`📋 [SCRIPT-MANAGER] Has script flow: ${!!campaignScript.script}`);
        console.log(`📋 [SCRIPT-MANAGER] Formatting instructions...`);

        const aiInstructions = this.formatCampaignInstructions(campaignScript);
        console.log(`✅ [SCRIPT-MANAGER] Formatted AI instructions:`, {
          length: aiInstructions.length,
          preview: aiInstructions.substring(0, 200) + '...',
          hasPersona: aiInstructions.includes('AGENT PERSONA'),
          hasScript: aiInstructions.includes('DETAILED SCRIPT FLOW'),
          hasCustomerData: aiInstructions.includes('CUSTOMER DATA STRUCTURE')
        });

        // Validate that we have meaningful instructions
        if (aiInstructions.trim().length < 100) {
          console.warn(`⚠️ [SCRIPT-MANAGER] AI instructions too short (${aiInstructions.length} chars), using fallback`);
          return this.createFallbackConfig(scriptId, scriptType === 'incoming');
        }

        return {
          aiInstructions: aiInstructions,
          voice: campaignScript.agentPersona?.voice || getConfigValue('ai.gemini.defaultVoice', 'Kore'),
          model: campaignScript.agentPersona?.model || getConfigValue('ai.gemini.defaultModel', 'gemini-2.5-flash-preview-native-audio-dialog'),
          isIncomingCall: scriptType === 'incoming',
          scriptType: scriptType,
          scriptId: scriptId,
          campaignId: campaignId
        };
      }

      // If no script found, use fallback instructions
      console.warn(`⚠️ [SCRIPT-MANAGER] No script found for ID: ${scriptId}, isIncoming: ${isIncoming}, using fallback instructions`);
      return this.createFallbackConfig(scriptId, isIncoming);
    } catch (error) {
      console.error('❌ [SCRIPT-MANAGER] Error in getScriptConfig:', error);
      if (error instanceof Error) {
        console.error('❌ [SCRIPT-MANAGER] Stack trace:', error.stack);
      }
      console.log('🔄 [SCRIPT-MANAGER] Using fallback instructions due to error');
      return this.createFallbackConfig(scriptId, isIncoming);
    }
  }

  /**
   * Format campaign script into AI instructions
   */
  private formatCampaignInstructions(campaignScript: CampaignScript): string {
    if (!campaignScript) {return '';}

    // Create comprehensive instructions from the campaign script
    let instructions = '';

    if (campaignScript.title) {
      instructions += `CAMPAIGN: ${campaignScript.title}\n\n`;
    }

    if (campaignScript.agentPersona) {
      instructions += `AGENT PERSONA:\n${JSON.stringify(campaignScript.agentPersona, null, 2)}\n\n`;
    }

    if (campaignScript.campaign) {
      instructions += `SCRIPT TO FOLLOW:\n${campaignScript.campaign}\n\n`;
    }

    // PERMANENT FIX: Include detailed script flow for better AI behavior
    if ((campaignScript as any).script) {
      instructions += `DETAILED SCRIPT FLOW:\n${JSON.stringify((campaignScript as any).script, null, 2)}\n\n`;
    }

    // Include customer data structure if available
    if ((campaignScript as any).customerData) {
      instructions += `CUSTOMER DATA STRUCTURE:\n${JSON.stringify((campaignScript as any).customerData, null, 2)}\n\n`;
    }

    // Include transfer data if available
    if ((campaignScript as any).transferData) {
      instructions += `TRANSFER INFORMATION:\n${JSON.stringify((campaignScript as any).transferData, null, 2)}\n\n`;
    }

    if (campaignScript.objectives) {
      instructions += `OBJECTIVES:\n${campaignScript.objectives.join('\n')}\n\n`;
    }

    if (campaignScript.keyPoints) {
      instructions += `KEY POINTS:\n${campaignScript.keyPoints.join('\n')}\n\n`;
    }

    instructions += `\nIMPORTANT: Follow this campaign script exactly. Stay in character and achieve the campaign objectives. Use the detailed script flow to guide the conversation structure.`;

    return instructions;
  }

  /**
   * Validate script data
   */
  private validateScript(scriptData: CustomScriptData): boolean {
    const required: (keyof CustomScriptData)[] = ['id', 'name', 'description'];
    for (const field of required) {
      if (!scriptData[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    return true;
  }

  /**
   * Create fallback configuration when script loading fails
   * @param scriptId - Script ID that failed to load
   * @param isIncoming - Whether this is an incoming call
   * @returns Fallback script configuration
   */
  private createFallbackConfig(scriptId: string | number, isIncoming: boolean): ScriptConfig {
    console.log(`🔄 [SCRIPT-MANAGER] Creating fallback config for script ${scriptId}, isIncoming: ${isIncoming}`);

    return {
      aiInstructions: this.fallbackInstructions,
      voice: getConfigValue('ai.gemini.defaultVoice', 'Kore'),
      model: getConfigValue('ai.gemini.defaultModel', 'gemini-2.5-flash-preview-native-audio-dialog'),
      isIncomingCall: isIncoming,
      scriptType: isIncoming ? 'incoming' : 'outbound',
      scriptId: String(scriptId),
      campaignId: typeof scriptId === 'number' ? scriptId : parseInt(String(scriptId)) || 1
    };
  }
}

// Export singleton instance
export const scriptManager = new ScriptManager();