# Cline Rules for Twilio Gemini Live API Project

## 🎯 PRIME DIRECTIVE: Simplicity Above All
**Always choose the simplest solution that works correctly.** Complexity must be justified by clear, measurable benefits. When in doubt, choose the simpler approach. See CLAUDE.md for detailed simplicity guidelines.

## Project Overview
This is a Node.js voice AI call center system that integrates:
- Twilio for telephony
- Google Gemini Live API for conversational AI  
- Supabase for data storage
- Next.js frontend
- ES modules throughout

## Key Architecture Principles
- **CAMPAIGN SCRIPTS ONLY** - 100% campaign scripts, 0% system prompts (see CAMPAIGN_SCRIPT_POLICY.md)
- **Policy-based AI instructions** - All AI behavior derived from campaign scripts, not system prompts
- **4 Flows Supported**: Outbound Twilio, Outbound Testing (browser), Inbound Twilio, Inbound Testing (browser)
- **Modular architecture** with clear separation of concerns

## Essential Commands
```bash
# Development
npm run dev          # Start with auto-reload
npm run debug        # Start with Node.js inspector

# Code Quality (ALWAYS run before completing tasks)
npm run lint         # Check code style
npm run lint:fix     # Auto-fix linting issues
npm test            # Run tests (ALL 27+ tests must pass)

# Production
npm start           # Start the server
pm2 start ecosystem.config.js --only twilio-gemini-backend
pm2 logs twilio-gemini-backend

# Health & Monitoring
npm run health      # Check server health
npm run monitor     # Run dev + health monitoring
npm run audio-quality # Check audio quality

# Maintenance
npm run clean       # Clean all generated files
npm run clean:audio # Clean audio debug files
```

## Port Allocation
```
5000 - verduona-web (Main Website)
5001 - template-live-api-web-console (Live Console)
5002 - template-live-audio (Live Audio)
5003 - agent-zero-verduona (Agent Zero Docker Container)
3005 - agent-verduona-login (Agent Auth Module - PM2)
3011 - twilio-gemini-frontend (Next.js auto-assigned)
3012 - twilio-openai-frontend (Next.js auto-assigned)
3101 - twilio-gemini-backend
3102 - twilio-openai-backend
3999 - auth-service (Nginx Auth Request Handler - CRITICAL)
```

## File Structure Rules

### Core Services Location
- **Entry Point**: `index.js` - Fastify server setup with WebSocket support
- **API Layer**: `src/api/` - REST endpoints for management and testing
- **Core Services**:
  - `src/session/session-manager.ts` - Central session orchestration
  - `src/gemini/client.ts` - Google Gemini AI integration
  - `src/websocket/handlers.ts` - Real-time audio streaming
  - `src/audio/audio-processor.ts` - Audio format conversion and processing
  - `src/config/config.ts` - Centralized configuration management

### Directory Structure
```
twilio-gemini-liveapi/
├── src/                           # Source code (TypeScript/JavaScript modules)
│   ├── api/                      # API route handlers
│   │   ├── management.ts         # Campaign and session management endpoints
│   │   ├── routes.ts            # Main API route definitions
│   │   └── testing.ts           # Testing and debugging endpoints
│   ├── audio/                   # Audio processing utilities
│   │   ├── audio-forwarding.ts  # Audio stream forwarding logic
│   │   ├── audio-processor.ts   # Format conversion (μ-law ↔ PCM)
│   │   └── transcription-manager.ts # Audio transcription handling
│   ├── config/                  # Configuration management
│   │   ├── audio-config.ts      # Audio processing settings
│   │   ├── business-config.ts   # Business logic configuration
│   │   ├── campaign-config.ts   # Campaign script settings
│   │   ├── config.ts           # Main configuration loader
│   │   └── localization-config.ts # Language/locale settings
│   ├── context/                 # Conversation context handling
│   │   └── conversation-context-manager.js # Context state management
│   ├── gemini/                  # Google Gemini AI integration
│   │   ├── client.ts           # Main Gemini API client
│   │   ├── model-manager.ts    # AI model configuration
│   │   └── voice-manager.ts    # Voice synthesis settings
│   ├── middleware/              # Express/Fastify middleware
│   │   ├── auth-simple.js      # Basic authentication
│   │   └── security-utils.js   # Input validation and sanitization
│   ├── scripts/                 # Campaign script management
│   │   ├── incoming-converter.js # Inbound script conversion
│   │   ├── script-cache.js     # Script caching system
│   │   └── script-manager.js   # Script loading and management
│   ├── session/                 # Session lifecycle management
│   │   ├── context-manager.ts  # Session context handling
│   │   ├── health-monitor.ts   # Session health monitoring
│   │   ├── lifecycle-manager.ts # Session creation/destruction
│   │   ├── recovery-manager.ts # Session recovery mechanisms
│   │   ├── session-manager.ts  # Main session orchestrator
│   │   └── summary-manager.ts  # Call summary generation
│   ├── types/                   # TypeScript type definitions
│   │   ├── custom/             # Custom type definitions
│   │   ├── global.d.ts         # Global type declarations
│   │   └── websocket.d.ts      # WebSocket-specific types
│   ├── utils/                   # Utility functions
│   │   ├── dotenv-stub.js      # Environment variable handling
│   │   ├── logger.js           # Structured logging utility
│   │   ├── test-utils.js       # Testing helper functions
│   │   ├── twilio-validation.js # Twilio webhook validation
│   │   └── websocket-utils.js  # WebSocket helper functions
│   └── websocket/               # WebSocket handling
│       ├── audio-handlers.ts    # Audio stream processing
│       ├── config-handlers.ts   # Configuration message handling
│       ├── handlers.ts          # Main WebSocket message router
│       ├── heartbeat-manager.ts # Connection health monitoring
│       ├── local-testing-handler.ts # Browser testing support
│       ├── session-events.ts    # Session event handling
│       ├── session-utils.ts     # Session utility functions
│       ├── start-session.ts     # Session initialization
│       └── twilio-flow-handler.ts # Twilio-specific flow handling
├── test/                        # Test files (Node.js test runner)
│   ├── helpers/                 # Test helper utilities
│   │   └── env.js              # Environment setup for tests
│   ├── *.test.js               # Individual test files
│   └── [component].test.js     # Component-specific tests
├── call-center-frontend/        # Next.js frontend application
├── docs/                        # Documentation files
├── public/                      # Static assets and files
├── middleware/                  # Legacy middleware (being migrated)
├── index.js                     # Main server entry point
└── package.json                # Dependencies and npm scripts
```

### Configuration
- All configuration centralized in `src/config/config.ts`
- Environment variables validated on startup
- Required: `GEMINI_API_KEY`, `TWILIO_*`, `PUBLIC_URL`

### Frontend Integration
- Next.js frontend in `call-center-frontend/`
- Backend API: Port 3101
- Frontend: Port 3011

### Testing Directory Structure
- **Test Location**: `test/` directory (root level)
- **Test Helpers**: `test/helpers/` for shared utilities
- **Naming Convention**: `[component-name].test.js`
- **Test Runner**: Node.js built-in test runner (not Jest)
- **Coverage**: All major components must have corresponding tests

## Code Style Rules

### Simplicity First
1. **Write obvious code** - If you need to explain it, it's too complex
2. **Avoid premature optimization** - Make it work, make it right, then (maybe) make it fast
3. **No clever tricks** - Clear code beats clever code every time
4. **Minimize abstractions** - Don't create interfaces/factories until you have 3+ implementations
5. **Direct is better than indirect** - Prefer direct function calls over complex event systems

### Audio Processing
1. **Audio Methods**: 
   - Browser: `geminiSession.sendRealtimeInput({ media: { data, mimeType } })`
   - Twilio: Same method after μ-law to PCM conversion
2. **Use `sendBrowserAudioToGemini` for browser audio**
3. **Use `sendAudioToGemini` for Twilio audio**

### Campaign Scripts
- All AI behavior comes from campaign scripts (IDs 1-12)
- Scripts 1-6: Outbound campaigns
- Scripts 7-12: Inbound campaigns
- **NO SYSTEM PROMPTS** - Send full campaign script as user message

### WebSocket Flows
- `/media-stream` - Twilio calls (inbound/outbound)
- `/local-audio-session` - Browser testing (legacy)
- `/test-outbound` - Outbound testing
- `/test-inbound` - Inbound testing

### Logging Rules
- **USE STRUCTURED LOGGER**: Always use `src/utils/logger.js` - NEVER console.log
- **Contextual logging**: Include callSid, sessionId, or relevant context
- **Emoji prefixes**: Required for all logs for easy scanning:
  - 🚀 Startup/initialization
  - 📞 Call events  
  - 🎤 Audio processing
  - 💬 AI responses
  - ❌ Errors
  - ⚠️ Warnings
  - ✅ Success
  - 🔍 Debug info
- **Performance timing**: Use `logger.timing()` for operations > 100ms
- **Structured format**: `logger.info('🎤 Audio processed', { chunks: 15, format: 'ulaw' })`
- **Error context**: Always include error object and relevant context

### Error Handling
- Always wrap async operations in try-catch
- Log errors with full context including callSid
- Implement graceful degradation
- Use recovery mechanisms for session failures

### Testing Rules
- **Comprehensive test coverage**: 27+ tests must ALL pass before completion
- **Use Node.js built-in test runner** (not Jest)
- **Test all 4 flows**: outbound/inbound × twilio/browser
- **Include LLM inference tests** with real Gemini API responses
- **Test files**:
  - `test/configuration.test.js` - 27 passing tests for config system
  - `test/backend-api.test.js` - API endpoint validation
  - `test/workflow-integration.test.js` - Complete workflow testing
- **Mock appropriately**: Use `src/utils/test-utils.js` utilities
- **Real API testing**: Include actual Gemini Live API inference tests
- **Error scenarios**: Test recovery mechanisms and error handling

## Modular Development Rules

### When modifying call handling:
1. **Keep it simple**: Don't add layers unless absolutely necessary
2. Check `src/websocket/handlers.js` for WebSocket message handling
3. Review `src/session/session-manager.js` for session lifecycle
4. Test all 4 flows after changes
5. **Ask yourself**: Can this be done more simply?

### When working with AI responses:
1. See `src/gemini/client.js` for Gemini integration
2. Configure voices in `src/gemini/voice-manager.js`
3. Adjust models in `src/gemini/model-manager.js`
4. **IMPORTANT**: Use Live API `setup` messages for session initialization, `sendRealtimeInput` for audio
5. **NEVER**: Use `sendClientContent` or `turnComplete` - these break continuous conversation

### When debugging audio issues:
1. Browser audio uses PCM16 @ 16kHz (no conversion needed)
2. Twilio audio uses μ-law @ 8kHz (needs conversion)
3. Check `src/audio/audio-processor.js` for conversion logic

## Code Quality Standards

### Before committing:
1. **Simplicity check**: Could this be simpler? Remove any unnecessary complexity
2. Run `npm run lint:fix`
3. Run `npm test` - **ALL 27+ tests must pass**
4. Test manually with all 4 flows
5. Check logs use proper emoji prefixes and structure
6. **Ask yourself**: Would a new developer understand this easily?

### File Modularity Rules
- **Keep files under 300 lines** - but don't split artificially
- **Single responsibility**: Each file should have one clear purpose
- **Extract utilities**: Move truly reusable functions to `src/utils/`
- **Avoid micro-modules**: Don't create files with just 1-2 functions
- **Colocate related code**: Keep related functions together
- **Break up large classes**: Only when they have multiple responsibilities
- **Separate concerns**: Keep business logic, API routes, and utilities separate
- **Prefer functions over classes**: Unless you need state or multiple instances

### Import/Export Rules
- Use ES modules (`import`/`export`) throughout
- No CommonJS (`require`/`module.exports`)
- Import from `src/` for internal modules
- Group imports: external packages, internal modules, relative imports

### Variable Naming
- Use camelCase for variables and functions
- Use PascalCase for classes
- Use UPPER_CASE for constants
- Use descriptive names (no single letters except loop counters)

## Security Rules
- Validate all inputs with `SecurityUtils` class
- Sanitize phone numbers, text, and JSON
- Use rate limiting on API endpoints
- Implement CORS properly for frontend integration
- Never log sensitive data (API keys, tokens)

## Performance Rules
- Use connection pooling for external APIs
- Implement caching where appropriate
- Monitor memory usage for long-running sessions
- Clean up inactive sessions and contexts
- Use compression for API responses

## Documentation Rules
- Update this file when architecture changes
- Document new environment variables in config.js
- Add JSDoc comments for complex functions
- Update README.md for deployment changes

## Common Pitfalls to Avoid
1. **Don't over-engineer** - Start simple, add complexity only when needed
2. **Don't use system prompts** - Only campaign scripts
3. **Don't mix audio methods** - Use correct method for source
4. **Don't skip error handling** - Always wrap risky operations
5. **Don't ignore test failures** - ALL 27+ tests must pass
6. **Don't hardcode configuration** - Use environment variables
7. **Don't forget session cleanup** - Prevent memory leaks
8. **Don't use console.log** - Use structured logger from `src/utils/logger.js`
9. **Don't create large files** - Keep under 300 lines, split into modules
10. **Don't skip emoji prefixes** - Required for all log messages
11. **Don't forget test coverage** - Include comprehensive workflow tests
12. **Don't add unnecessary dependencies** - Use built-in modules when possible
13. **Don't create deep inheritance** - Prefer composition over inheritance
14. **Don't abstract too early** - Wait until you have real use cases

## Git Workflow
- Work on feature branches
- Merge to `develop` for testing
- Only merge to `main` when fully tested
- Include tests in all PRs
- Update version numbers appropriately

## Recovery Procedures & Build/Restart Guide

### Quick Recovery After Server Crash/Restart
```bash
# Navigate to project root
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi

# Restore all PM2 processes from saved state
pm2 resurrect

# Wait for processes to initialize
sleep 10

# Restart all for fresh state
pm2 restart all

# Wait for services to stabilize
sleep 15

# Verify all services are running
pm2 status
```

### Manual Recovery (If PM2 dump is corrupted)
```bash
# Navigate to project root
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi

# Start core services
pm2 start ecosystem.config.js --only twilio-gemini-backend
pm2 start ecosystem.config.js --only twilio-gemini-frontend

# Save current state for future recovery
pm2 save
```

### Production Deployment Process (CRITICAL)
```bash
# Build applications for production
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi/call-center-frontend
pnpm build

# Start in production mode using ecosystem configs
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi
pm2 start ecosystem.config.js --only twilio-gemini-frontend

# Verify production status
pm2 list
pm2 logs twilio-gemini-frontend --lines 5

# Test direct frontend health
curl -I http://localhost:3011  # Should return 200

# ⚠️ CRITICAL: Must use `pnpm build` then `pnpm start`, never `pnpm dev` in production
```

### Health Check Commands
```bash
# Check service health
pm2 status

# Check backend API health
curl https://gemini-api.verduona.com/health

# Check frontend health
curl -I http://localhost:3011

# Check logs for errors
pm2 logs --lines 20 --err

# Check disk space and memory
df -h && free -h
```

### Emergency Recovery Commands
```bash
# Emergency restart of all services
pm2 restart twilio-gemini-backend
pm2 restart twilio-gemini-frontend

# Rebuild and restart if needed
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi/call-center-frontend
pnpm install && pnpm build
cd /home/<USER>/github/verduona-full/twilio-gemini-liveapi
pm2 restart twilio-gemini-frontend

# Check nginx configuration
sudo nginx -t
sudo systemctl reload nginx
```

## Environment Variables Required
- `GEMINI_API_KEY` - Required for AI functionality
- `TWILIO_ACCOUNT_SID` - Required for phone service
- `TWILIO_AUTH_TOKEN` - Required for phone service
- `PUBLIC_URL` - Required for Twilio callbacks
- Optional: `DEEPGRAM_API_KEY`, `OPENAI_API_KEY`, `SUPABASE_*`
